#!/bin/bash
set -e

# Kritrima-AI WSL Build Script
# This script fixes common build errors and installs Kritrima-AI

echo "Starting Kritrima-AI build and install process..."

# Get the project path from argument or use current directory
PROJECT_PATH="${1:-$(pwd)}"
CLEAN_BUILD="${2:-false}"

echo "Project path: $PROJECT_PATH"

# Navigate to project directory
cd "$PROJECT_PATH"
echo "Working in: $(pwd)"

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found in $(pwd)"
    echo "Please make sure you're in the correct project directory."
    exit 1
fi

echo "Found package.json, proceeding with build..."

# Clean previous builds if requested
if [ "$CLEAN_BUILD" = "true" ]; then
    echo "Cleaning previous build artifacts..."
    rm -rf node_modules dist .pnpm-store package-lock.json 2>/dev/null || true
fi

# Install dependencies using npm (more reliable in WSL than pnpm)
echo "Installing dependencies with npm..."
npm install

# Build the project
echo "Building the project..."
npm run build

# Install globally
echo "Installing globally..."
npm install -g .

# Test the installation
echo "Testing installation..."
if command -v kritrima-ai &> /dev/null; then
    echo "SUCCESS: Kritrima-AI installed successfully!"
    echo "Version: $(kritrima-ai --version)"
    echo ""
    echo "Installation complete! You can now use 'kritrima-ai' command."
    echo ""
    echo "Quick start:"
    echo "   kritrima-ai --help"
    echo "   kritrima-ai 'explain this codebase'"
else
    echo "ERROR: Installation failed - kritrima-ai command not found"
    exit 1
fi

echo "Build and installation completed successfully!"
