# Kritrima-AI WSL Build and Install Script
# This script fixes build errors and installs Kritrima-AI in WSL
# Designed to be run with a simple double-click from Windows

param(
    [string]$ProjectPath = "",
    [switch]$Clean,
    [switch]$Help
)

# Colors for output
$Red = "`e[31m"
$Green = "`e[32m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Cyan = "`e[36m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = $Reset)
    Write-Host "$Color$Message$Reset"
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 70 $Blue
    Write-ColorOutput "  $Title" $Blue
    Write-ColorOutput "=" * 70 $Blue
    Write-Host ""
}

function Write-Step {
    param([string]$Message)
    Write-ColorOutput "▶ $Message" $Cyan
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" $Green
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" $Red
}

function Show-Usage {
    Write-Header "Kritrima-AI WSL Build and Install Script"

    Write-Host "This script automatically fixes build errors and installs Kritrima-AI in WSL."
    Write-Host "It handles platform-specific issues like esbuild platform mismatches and permission errors."
    Write-Host ""
    Write-Host "Usage: .\build-and-install-wsl.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -ProjectPath [path]   Specify the project directory (auto-detected if not provided)"
    Write-Host "  -Clean               Clean all build artifacts before building"
    Write-Host "  -Help                Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\build-and-install-wsl.ps1"
    Write-Host "  .\build-and-install-wsl.ps1 -Clean"
    Write-Host "  .\build-and-install-wsl.ps1 -ProjectPath 'C:\Users\<USER>\OneDrive\Documents\Kritrima AI'"
    Write-Host ""
    Write-ColorOutput "This script can be run by double-clicking from Windows Explorer!" $Green
}

function Find-ProjectPath {
    # Try to auto-detect project path
    $currentDir = Get-Location
    $scriptDir = Split-Path -Parent $MyInvocation.ScriptName

    # Check if we're in the project directory
    if (Test-Path "$currentDir\package.json") {
        return $currentDir.Path
    }

    # Check if script is in scripts folder of project
    $parentDir = Split-Path -Parent $scriptDir
    if (Test-Path "$parentDir\package.json") {
        return $parentDir
    }

    # Check common locations
    $commonPaths = @(
        "C:\Users\<USER>\OneDrive\Documents\Kritrima AI",
        "C:\Users\<USER>\OneDrive\Documents\Kritrima AI",
        "$env:USERPROFILE\OneDrive\Documents\Kritrima AI",
        "$env:USERPROFILE\Documents\Kritrima AI"
    )

    foreach ($path in $commonPaths) {
        if (Test-Path "$path\package.json") {
            return $path
        }
    }

    return $null
}

function Test-WSLAvailable {
    try {
        $result = wsl --version 2>$null
        return $true
    }
    catch {
        return $false
    }
}

function Build-And-Install-KritrimaAI {
    param([string]$ProjectPath)

    Write-Header "Building and Installing Kritrima-AI in WSL"

    if (-not (Test-WSLAvailable)) {
        Write-Error "WSL is not available. Please install WSL first."
        Write-Host "Run: wsl --install"
        exit 1
    }

    # Convert Windows path to WSL path
    $wslPath = $ProjectPath -replace '^([A-Z]):', '/mnt/$1' -replace '\\', '/' -replace ' ', '\ '
    $wslPath = $wslPath.ToLower()

    Write-Step "Project path: $ProjectPath"
    Write-Step "WSL path: $wslPath"

    try {
        Write-Step "Executing build script in WSL..."

        # Get the script directory and build script path
        $scriptDir = Split-Path -Parent $MyInvocation.ScriptName
        $buildScriptPath = Join-Path $scriptDir "build-kritrima-ai.sh"

        # Convert build script path to WSL format
        $wslScriptPath = $buildScriptPath -replace '^([A-Z]):', '/mnt/$1' -replace '\\', '/' -replace ' ', '\ '
        $wslScriptPath = $wslScriptPath.ToLower()

        # Set clean flag
        $cleanFlag = if ($Clean) { "true" } else { "false" }

        Write-Step "Using build script: $wslScriptPath"

        # Execute the build script in WSL
        wsl bash "$wslScriptPath" "$wslPath" "$cleanFlag"

        Write-Success "Build and installation completed successfully!"
        Write-Host ""
        Write-ColorOutput "Next steps:" $Blue
        Write-Host "1. Open WSL terminal: wsl"
        Write-Host "2. Run: kritrima-ai --help"
        Write-Host "3. Start using: kritrima-ai 'your prompt here'"

    }
    catch {
        Write-Error "Build failed: $_"
        Write-Host ""
        Write-ColorOutput "Troubleshooting tips:" $Yellow
        Write-Host "1. Make sure WSL is properly installed and running"
        Write-Host "2. Ensure you have Node.js 22+ installed in WSL"
        Write-Host "3. Try running with -Clean flag to remove old artifacts"
        Write-Host "4. Check that the project path is correct"
        exit 1
    }
}

# Main execution
if ($Help) {
    Show-Usage
    exit 0
}

# Auto-detect project path if not provided
if (-not $ProjectPath) {
    $ProjectPath = Find-ProjectPath
    if (-not $ProjectPath) {
        Write-Error "Could not find Kritrima-AI project directory."
        Write-Host "Please specify the path using -ProjectPath parameter."
        Write-Host ""
        Show-Usage
        exit 1
    }
}

# Validate project path
if (-not (Test-Path "$ProjectPath\package.json")) {
    Write-Error "Invalid project path: $ProjectPath"
    Write-Error "package.json not found in the specified directory."
    exit 1
}

# Run the build and install process
Build-And-Install-KritrimaAI -ProjectPath $ProjectPath

Write-Host ""
Write-ColorOutput "All done! Kritrima-AI is ready to use in WSL." $Green
Write-Host ""
