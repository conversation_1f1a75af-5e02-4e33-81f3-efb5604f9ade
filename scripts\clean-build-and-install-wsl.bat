@echo off
REM Kritrima-AI WSL Clean Build and Install - Double-click to run
REM This batch file performs a clean build (removes all artifacts first)

echo.
echo ========================================
echo   Kritrima-AI WSL Clean Build and Install
echo ========================================
echo.
echo This will perform a CLEAN build and install Kritrima-AI in WSL.
echo It will remove all previous build artifacts and dependencies.
echo.

REM Check if WSL is available
where wsl >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: WSL is not available.
    echo Please install WSL first: wsl --install
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"

REM Auto-detect project path (assume batch file is in scripts folder)
for %%I in ("%SCRIPT_DIR%..") do set "PROJECT_PATH=%%~fI"

REM Convert Windows path to WSL path
set "WSL_PROJECT_PATH=%PROJECT_PATH%"
set "WSL_PROJECT_PATH=%WSL_PROJECT_PATH:C:=/mnt/c%"
set "WSL_PROJECT_PATH=%WSL_PROJECT_PATH:\=/%"

REM Convert script path to WSL path
set "WSL_SCRIPT_PATH=%SCRIPT_DIR%build-kritrima-ai.sh"
set "WSL_SCRIPT_PATH=%WSL_SCRIPT_PATH:C:=/mnt/c%"
set "WSL_SCRIPT_PATH=%WSL_SCRIPT_PATH:\=/%"

echo Project path: %PROJECT_PATH%
echo WSL project path: %WSL_PROJECT_PATH%
echo.
echo Starting CLEAN build process...
echo This will remove node_modules, dist, and other build artifacts.
echo.

REM Run the bash script in WSL with clean flag
wsl bash "%WSL_SCRIPT_PATH%" "%WSL_PROJECT_PATH%" "true"

REM Check if the script succeeded
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo   Clean build completed successfully!
    echo ========================================
    echo.
    echo You can now use 'kritrima-ai' in WSL.
    echo.
    echo To test: 
    echo   1. Open WSL: wsl
    echo   2. Run: kritrima-ai --help
    echo.
) else (
    echo.
    echo ========================================
    echo   Clean build failed!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo You may need to install Node.js in WSL first.
    echo.
)

echo Press any key to exit...
pause >nul
